{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/profile/profile-header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { GamerProfile } from '@/types/gamer';\nimport { \n  MapPin, \n  Clock, \n  Calendar, \n  Edit3, \n  Users, \n  Trophy,\n  Gamepad2,\n  Shield,\n  Sword,\n  Heart\n} from 'lucide-react';\n\ninterface ProfileHeaderProps {\n  profile: GamerProfile;\n  isOwnProfile?: boolean;\n  onProfileUpdate?: (updates: Partial<GamerProfile>) => void;\n}\n\nexport function ProfileHeader({ profile, isOwnProfile = false, onProfileUpdate }: ProfileHeaderProps) {\n  const [isEditing, setIsEditing] = useState(false);\n  const [editForm, setEditForm] = useState({\n    displayName: profile.displayName,\n    bio: profile.bio || '',\n    location: profile.location || '',\n  });\n\n  const handleSave = () => {\n    onProfileUpdate?.(editForm);\n    setIsEditing(false);\n  };\n\n  const getPlayStyleIcon = (playStyle: string) => {\n    switch (playStyle) {\n      case 'competitive':\n        return <Sword className=\"h-4 w-4\" />;\n      case 'casual':\n        return <Heart className=\"h-4 w-4\" />;\n      default:\n        return <Shield className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getPlayStyleColor = (playStyle: string) => {\n    switch (playStyle) {\n      case 'competitive':\n        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      case 'casual':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      default:\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n    }\n  };\n\n  return (\n    <Card className=\"w-full\">\n      <CardContent className=\"p-6\">\n        <div className=\"flex flex-col md:flex-row gap-6\">\n          {/* Avatar and Online Status */}\n          <div className=\"flex flex-col items-center md:items-start\">\n            <div className=\"relative\">\n              <Avatar className=\"h-24 w-24 md:h-32 md:w-32\">\n                <AvatarImage src={profile.avatar} alt={profile.displayName} />\n                <AvatarFallback className=\"text-2xl\">\n                  {profile.displayName.slice(0, 2).toUpperCase()}\n                </AvatarFallback>\n              </Avatar>\n              {profile.isOnline && (\n                <div\n                  className=\"absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full\"\n                  aria-label=\"User is online\"\n                  title=\"Online\"\n                />\n              )}\n            </div>\n            <div className=\"mt-2 text-center md:text-left\">\n              <Badge variant={profile.isOnline ? 'default' : 'secondary'} className=\"text-xs\">\n                {profile.isOnline ? 'Online' : 'Offline'}\n              </Badge>\n            </div>\n          </div>\n\n          {/* Profile Information */}\n          <div className=\"flex-1 space-y-4\">\n            <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n              <div>\n                <h1 className=\"text-2xl md:text-3xl font-bold\">{profile.displayName}</h1>\n                <p className=\"text-muted-foreground\">@{profile.username}</p>\n              </div>\n              \n              {isOwnProfile && (\n                <Dialog open={isEditing} onOpenChange={setIsEditing}>\n                  <DialogTrigger asChild>\n                    <Button variant=\"outline\" size=\"sm\" aria-label=\"Edit profile information\">\n                      <Edit3 className=\"h-4 w-4 mr-2\" aria-hidden=\"true\" />\n                      Edit Profile\n                    </Button>\n                  </DialogTrigger>\n                  <DialogContent className=\"sm:max-w-[425px]\">\n                    <DialogHeader>\n                      <DialogTitle>Edit Profile</DialogTitle>\n                    </DialogHeader>\n                    <div className=\"grid gap-4 py-4\">\n                      <div className=\"grid gap-2\">\n                        <Label htmlFor=\"displayName\">Display Name</Label>\n                        <Input\n                          id=\"displayName\"\n                          value={editForm.displayName}\n                          onChange={(e) => setEditForm({ ...editForm, displayName: e.target.value })}\n                          aria-describedby=\"displayName-description\"\n                        />\n                        <p id=\"displayName-description\" className=\"sr-only\">\n                          Enter your display name as it will appear to other users\n                        </p>\n                      </div>\n                      <div className=\"grid gap-2\">\n                        <Label htmlFor=\"bio\">Bio</Label>\n                        <Textarea\n                          id=\"bio\"\n                          value={editForm.bio}\n                          onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}\n                          rows={3}\n                          aria-describedby=\"bio-description\"\n                          maxLength={500}\n                        />\n                        <p id=\"bio-description\" className=\"text-xs text-muted-foreground\">\n                          Tell other gamers about yourself (max 500 characters)\n                        </p>\n                      </div>\n                      <div className=\"grid gap-2\">\n                        <Label htmlFor=\"location\">Location</Label>\n                        <Input\n                          id=\"location\"\n                          value={editForm.location}\n                          onChange={(e) => setEditForm({ ...editForm, location: e.target.value })}\n                        />\n                      </div>\n                    </div>\n                    <div className=\"flex justify-end gap-2\">\n                      <Button variant=\"outline\" onClick={() => setIsEditing(false)}>\n                        Cancel\n                      </Button>\n                      <Button onClick={handleSave}>Save Changes</Button>\n                    </div>\n                  </DialogContent>\n                </Dialog>\n              )}\n            </div>\n\n            {/* Bio */}\n            {profile.bio && (\n              <p className=\"text-sm text-muted-foreground leading-relaxed\">\n                {profile.bio}\n              </p>\n            )}\n\n            {/* Profile Details */}\n            <div className=\"flex flex-wrap gap-4 text-sm text-muted-foreground\">\n              {profile.location && (\n                <div className=\"flex items-center gap-1\">\n                  <MapPin className=\"h-4 w-4\" />\n                  <span>{profile.location}</span>\n                </div>\n              )}\n              {profile.timezone && (\n                <div className=\"flex items-center gap-1\">\n                  <Clock className=\"h-4 w-4\" />\n                  <span>{profile.timezone}</span>\n                </div>\n              )}\n              <div className=\"flex items-center gap-1\">\n                <Calendar className=\"h-4 w-4\" />\n                <span>Joined {profile.joinDate.toLocaleDateString()}</span>\n              </div>\n            </div>\n\n            {/* Play Style and Stats */}\n            <div className=\"flex flex-wrap gap-2\">\n              <Badge className={`${getPlayStyleColor(profile.playStyle)} flex items-center gap-1`}>\n                {getPlayStyleIcon(profile.playStyle)}\n                {profile.playStyle.charAt(0).toUpperCase() + profile.playStyle.slice(1)} Player\n              </Badge>\n              \n              <Badge variant=\"outline\" className=\"flex items-center gap-1\">\n                <Gamepad2 className=\"h-4 w-4\" />\n                {profile.stats.gamesOwned} Games\n              </Badge>\n              \n              <Badge variant=\"outline\" className=\"flex items-center gap-1\">\n                <Trophy className=\"h-4 w-4\" />\n                {profile.stats.achievementsUnlocked} Achievements\n              </Badge>\n              \n              <Badge variant=\"outline\" className=\"flex items-center gap-1\">\n                <Users className=\"h-4 w-4\" />\n                {profile.stats.friendsCount} Friends\n              </Badge>\n            </div>\n\n            {/* Preferred Roles */}\n            {profile.preferredRoles.length > 0 && (\n              <div>\n                <h3 className=\"text-sm font-medium mb-2\">Preferred Roles</h3>\n                <div className=\"flex flex-wrap gap-1\">\n                  {profile.preferredRoles.map((role) => (\n                    <Badge key={role} variant=\"secondary\" className=\"text-xs\">\n                      {role}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;AA+BO,SAAS,cAAc,EAAE,OAAO,EAAE,eAAe,KAAK,EAAE,eAAe,EAAsB;IAClG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa,QAAQ,WAAW;QAChC,KAAK,QAAQ,GAAG,IAAI;QACpB,UAAU,QAAQ,QAAQ,IAAI;IAChC;IAEA,MAAM,aAAa;QACjB,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,QAAQ,MAAM;gDAAE,KAAK,QAAQ,WAAW;;;;;;0DAC1D,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;oCAG/C,QAAQ,QAAQ,kBACf,8OAAC;wCACC,WAAU;wCACV,cAAW;wCACX,OAAM;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAS,QAAQ,QAAQ,GAAG,YAAY;oCAAa,WAAU;8CACnE,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkC,QAAQ,WAAW;;;;;;0DACnE,8OAAC;gDAAE,WAAU;;oDAAwB;oDAAE,QAAQ,QAAQ;;;;;;;;;;;;;oCAGxD,8BACC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAM;wCAAW,cAAc;;0DACrC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,cAAW;;sEAC7C,8OAAC,0MAAA,CAAA,QAAK;4DAAC,WAAU;4DAAe,eAAY;;;;;;wDAAS;;;;;;;;;;;;0DAIzD,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,kIAAA,CAAA,eAAY;kEACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;sEAAC;;;;;;;;;;;kEAEf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAc;;;;;;kFAC7B,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,SAAS,WAAW;wEAC3B,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACxE,oBAAiB;;;;;;kFAEnB,8OAAC;wEAAE,IAAG;wEAA0B,WAAU;kFAAU;;;;;;;;;;;;0EAItD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAM;;;;;;kFACrB,8OAAC,oIAAA,CAAA,WAAQ;wEACP,IAAG;wEACH,OAAO,SAAS,GAAG;wEACnB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAChE,MAAM;wEACN,oBAAiB;wEACjB,WAAW;;;;;;kFAEb,8OAAC;wEAAE,IAAG;wEAAkB,WAAU;kFAAgC;;;;;;;;;;;;0EAIpE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAW;;;;;;kFAC1B,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,SAAS,QAAQ;wEACxB,UAAU,CAAC,IAAM,YAAY;gFAAE,GAAG,QAAQ;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAC;;;;;;;;;;;;;;;;;;kEAI3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,aAAa;0EAAQ;;;;;;0EAG9D,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAS;0EAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQtC,QAAQ,GAAG,kBACV,8OAAC;gCAAE,WAAU;0CACV,QAAQ,GAAG;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;oCACZ,QAAQ,QAAQ,kBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,QAAQ,QAAQ;;;;;;;;;;;;oCAG1B,QAAQ,QAAQ,kBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,QAAQ,QAAQ;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;;oDAAK;oDAAQ,QAAQ,QAAQ,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;0CAKrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAW,GAAG,kBAAkB,QAAQ,SAAS,EAAE,wBAAwB,CAAC;;4CAChF,iBAAiB,QAAQ,SAAS;4CAClC,QAAQ,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,SAAS,CAAC,KAAK,CAAC;4CAAG;;;;;;;kDAG1E,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,QAAQ,KAAK,CAAC,UAAU;4CAAC;;;;;;;kDAG5B,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,QAAQ,KAAK,CAAC,oBAAoB;4CAAC;;;;;;;kDAGtC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,QAAQ,KAAK,CAAC,YAAY;4CAAC;;;;;;;;;;;;;4BAK/B,QAAQ,cAAc,CAAC,MAAM,GAAG,mBAC/B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC,iIAAA,CAAA,QAAK;gDAAY,SAAQ;gDAAY,WAAU;0DAC7C;+CADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhC", "debugId": null}}, {"offset": {"line": 1257, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/profile/gaming-stats.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport { GamerStats } from '@/types/gamer';\nimport { \n  Clock, \n  Trophy, \n  Users, \n  Gamepad2, \n  Target,\n  TrendingUp,\n  Star,\n  Timer\n} from 'lucide-react';\n\ninterface GamingStatsProps {\n  stats: GamerStats;\n}\n\nexport function GamingStats({ stats }: GamingStatsProps) {\n  const formatHours = (hours: number) => {\n    if (hours < 24) {\n      return `${hours}h`;\n    }\n    const days = Math.floor(hours / 24);\n    const remainingHours = hours % 24;\n    return `${days}d ${remainingHours}h`;\n  };\n\n  const formatSessionLength = (minutes: number) => {\n    if (minutes < 60) {\n      return `${minutes}m`;\n    }\n    const hours = Math.floor(minutes / 60);\n    const remainingMinutes = minutes % 60;\n    return `${hours}h ${remainingMinutes}m`;\n  };\n\n  const getAchievementProgress = () => {\n    // Mock calculation for achievement progress\n    const totalPossibleAchievements = 100;\n    return (stats.achievementsUnlocked / totalPossibleAchievements) * 100;\n  };\n\n  const getPlaytimeRank = () => {\n    if (stats.totalHoursPlayed > 5000) return { rank: 'Legendary', color: 'text-yellow-600' };\n    if (stats.totalHoursPlayed > 2000) return { rank: 'Master', color: 'text-purple-600' };\n    if (stats.totalHoursPlayed > 1000) return { rank: 'Expert', color: 'text-blue-600' };\n    if (stats.totalHoursPlayed > 500) return { rank: 'Advanced', color: 'text-green-600' };\n    return { rank: 'Novice', color: 'text-gray-600' };\n  };\n\n  const playtimeRank = getPlaytimeRank();\n\n  return (\n    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n      {/* Total Playtime */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium\">Total Playtime</CardTitle>\n          <Clock className=\"h-4 w-4 text-muted-foreground\" />\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-2xl font-bold\">{formatHours(stats.totalHoursPlayed)}</div>\n          <div className=\"flex items-center gap-2 mt-2\">\n            <Badge variant=\"outline\" className={playtimeRank.color}>\n              <Star className=\"h-3 w-3 mr-1\" />\n              {playtimeRank.rank}\n            </Badge>\n          </div>\n          <p className=\"text-xs text-muted-foreground mt-2\">\n            Avg session: {formatSessionLength(stats.averageSessionLength)}\n          </p>\n        </CardContent>\n      </Card>\n\n      {/* Achievements */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium\">Achievements</CardTitle>\n          <Trophy className=\"h-4 w-4 text-muted-foreground\" />\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-2xl font-bold\">{stats.achievementsUnlocked}</div>\n          <div className=\"mt-2\">\n            <Progress value={getAchievementProgress()} className=\"h-2\" />\n            <p className=\"text-xs text-muted-foreground mt-1\">\n              {getAchievementProgress().toFixed(0)}% completion rate\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Games Library */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium\">Games Library</CardTitle>\n          <Gamepad2 className=\"h-4 w-4 text-muted-foreground\" />\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-2xl font-bold\">{stats.gamesOwned}</div>\n          {stats.favoriteGame && (\n            <div className=\"mt-2\">\n              <p className=\"text-xs text-muted-foreground\">Favorite Game</p>\n              <p className=\"text-sm font-medium\">{stats.favoriteGame}</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Social Stats */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium\">Social</CardTitle>\n          <Users className=\"h-4 w-4 text-muted-foreground\" />\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-muted-foreground\">Friends</span>\n              <span className=\"text-sm font-medium\">{stats.friendsCount}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-muted-foreground\">Guilds</span>\n              <span className=\"text-sm font-medium\">{stats.guildsJoined}</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Gaming Focus */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium\">Gaming Focus</CardTitle>\n          <Target className=\"h-4 w-4 text-muted-foreground\" />\n        </CardHeader>\n        <CardContent>\n          {stats.mostPlayedGenre && (\n            <div>\n              <p className=\"text-xs text-muted-foreground\">Most Played Genre</p>\n              <p className=\"text-sm font-medium capitalize\">{stats.mostPlayedGenre}</p>\n            </div>\n          )}\n          <div className=\"mt-3\">\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              <TrendingUp className=\"h-3 w-3 mr-1\" />\n              Active Player\n            </Badge>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Session Info */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium\">Session Stats</CardTitle>\n          <Timer className=\"h-4 w-4 text-muted-foreground\" />\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2\">\n            <div>\n              <p className=\"text-xs text-muted-foreground\">Average Session</p>\n              <p className=\"text-sm font-medium\">{formatSessionLength(stats.averageSessionLength)}</p>\n            </div>\n            <div>\n              <p className=\"text-xs text-muted-foreground\">Total Sessions</p>\n              <p className=\"text-sm font-medium\">\n                {Math.round(stats.totalHoursPlayed * 60 / stats.averageSessionLength)}\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAqBO,SAAS,YAAY,EAAE,KAAK,EAAoB;IACrD,MAAM,cAAc,CAAC;QACnB,IAAI,QAAQ,IAAI;YACd,OAAO,GAAG,MAAM,CAAC,CAAC;QACpB;QACA,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;QAChC,MAAM,iBAAiB,QAAQ;QAC/B,OAAO,GAAG,KAAK,EAAE,EAAE,eAAe,CAAC,CAAC;IACtC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,UAAU,IAAI;YAChB,OAAO,GAAG,QAAQ,CAAC,CAAC;QACtB;QACA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;IACzC;IAEA,MAAM,yBAAyB;QAC7B,4CAA4C;QAC5C,MAAM,4BAA4B;QAClC,OAAO,AAAC,MAAM,oBAAoB,GAAG,4BAA6B;IACpE;IAEA,MAAM,kBAAkB;QACtB,IAAI,MAAM,gBAAgB,GAAG,MAAM,OAAO;YAAE,MAAM;YAAa,OAAO;QAAkB;QACxF,IAAI,MAAM,gBAAgB,GAAG,MAAM,OAAO;YAAE,MAAM;YAAU,OAAO;QAAkB;QACrF,IAAI,MAAM,gBAAgB,GAAG,MAAM,OAAO;YAAE,MAAM;YAAU,OAAO;QAAgB;QACnF,IAAI,MAAM,gBAAgB,GAAG,KAAK,OAAO;YAAE,MAAM;YAAY,OAAO;QAAiB;QACrF,OAAO;YAAE,MAAM;YAAU,OAAO;QAAgB;IAClD;IAEA,MAAM,eAAe;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CAAsB,YAAY,MAAM,gBAAgB;;;;;;0CACvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAW,aAAa,KAAK;;sDACpD,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,aAAa,IAAI;;;;;;;;;;;;0CAGtB,8OAAC;gCAAE,WAAU;;oCAAqC;oCAClC,oBAAoB,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;0BAMlE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAEpB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CAAsB,MAAM,oBAAoB;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,OAAO;wCAA0B,WAAU;;;;;;kDACrD,8OAAC;wCAAE,WAAU;;4CACV,yBAAyB,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CAAsB,MAAM,UAAU;;;;;;4BACpD,MAAM,YAAY,kBACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAuB,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;sDAChD,8OAAC;4CAAK,WAAU;sDAAuB,MAAM,YAAY;;;;;;;;;;;;8CAE3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;sDAChD,8OAAC;4CAAK,WAAU;sDAAuB,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAEpB,8OAAC,gIAAA,CAAA,cAAW;;4BACT,MAAM,eAAe,kBACpB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAkC,MAAM,eAAe;;;;;;;;;;;;0CAGxE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAuB,oBAAoB,MAAM,oBAAoB;;;;;;;;;;;;8CAEpF,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK,CAAC,MAAM,gBAAgB,GAAG,KAAK,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpF", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/profile/games-characters.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { GameProfile, Character } from '@/types/gamer';\nimport { \n  Clock, \n  Trophy, \n  Server, \n  Star,\n  Shield,\n  Sword,\n  Crown,\n  Plus,\n  ExternalLink\n} from 'lucide-react';\n\ninterface GamesCharactersProps {\n  games: GameProfile[];\n  isOwnProfile?: boolean;\n}\n\nexport function GamesCharacters({ games, isOwnProfile = false }: GamesCharactersProps) {\n  const [selectedGame, setSelectedGame] = useState<string>(games[0]?.gameId || '');\n\n  const formatHours = (hours: number) => {\n    if (hours < 24) {\n      return `${hours}h`;\n    }\n    const days = Math.floor(hours / 24);\n    const remainingHours = hours % 24;\n    return `${days}d ${remainingHours}h`;\n  };\n\n  const getPlatformColor = (platform: string) => {\n    const colors: Record<string, string> = {\n      steam: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      battlenet: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      epic: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n      playstation: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      xbox: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n      nintendo: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    };\n    return colors[platform] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n  };\n\n  const getCharacterIcon = (characterClass?: string) => {\n    // Simple mapping of classes to icons\n    const classIcons: Record<string, React.ReactNode> = {\n      'Death Knight': <Sword className=\"h-4 w-4\" />,\n      'Paladin': <Shield className=\"h-4 w-4\" />,\n      'Dark Knight': <Sword className=\"h-4 w-4\" />,\n    };\n    return classIcons[characterClass || ''] || <Star className=\"h-4 w-4\" />;\n  };\n\n  const CharacterCard = ({ character, game }: { character: Character; game: GameProfile }) => (\n    <Card className=\"relative\">\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <Avatar className=\"h-12 w-12\">\n              <AvatarImage src={character.avatar} alt={character.name} />\n              <AvatarFallback>\n                {getCharacterIcon(character.class)}\n              </AvatarFallback>\n            </Avatar>\n            <div>\n              <div className=\"flex items-center gap-2\">\n                <h4 className=\"font-semibold\">{character.name}</h4>\n                {character.isPrimary && (\n                  <Crown className=\"h-4 w-4 text-yellow-500\" title=\"Main Character\" />\n                )}\n              </div>\n              <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                <span>Level {character.level}</span>\n                {character.class && (\n                  <>\n                    <span>•</span>\n                    <span>{character.class}</span>\n                  </>\n                )}\n                {character.race && (\n                  <>\n                    <span>•</span>\n                    <span>{character.race}</span>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"text-right\">\n            <Badge variant=\"outline\" className=\"text-xs\">\n              Lv. {character.level}\n            </Badge>\n            {character.itemLevel && (\n              <p className=\"text-xs text-muted-foreground mt-1\">\n                iLvl {character.itemLevel}\n              </p>\n            )}\n          </div>\n        </div>\n\n        <div className=\"mt-3 space-y-1\">\n          {character.server && (\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              <Server className=\"h-3 w-3\" />\n              <span>{character.server}</span>\n              {character.faction && (\n                <>\n                  <span>•</span>\n                  <span>{character.faction}</span>\n                </>\n              )}\n            </div>\n          )}\n          {character.specialization && (\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              {character.specialization}\n            </Badge>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n\n  const GameCard = ({ game }: { game: GameProfile }) => (\n    <Card className=\"cursor-pointer transition-colors hover:bg-muted/50\" \n          onClick={() => setSelectedGame(game.gameId)}>\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <Avatar className=\"h-12 w-12\">\n              <AvatarImage src={game.gameIcon} alt={game.gameName} />\n              <AvatarFallback>\n                {game.gameName.slice(0, 2).toUpperCase()}\n              </AvatarFallback>\n            </Avatar>\n            <div>\n              <h3 className=\"font-semibold\">{game.gameName}</h3>\n              <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                <Clock className=\"h-3 w-3\" />\n                <span>{formatHours(game.hoursPlayed)}</span>\n                {game.level && (\n                  <>\n                    <span>•</span>\n                    <span>Level {game.level}</span>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"text-right space-y-1\">\n            <Badge className={getPlatformColor(game.platform)} variant=\"secondary\">\n              {game.platform.toUpperCase()}\n            </Badge>\n            {game.rank && (\n              <p className=\"text-xs text-muted-foreground\">{game.rank}</p>\n            )}\n          </div>\n        </div>\n\n        <div className=\"mt-3 flex items-center justify-between\">\n          <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n            {game.server && (\n              <div className=\"flex items-center gap-1\">\n                <Server className=\"h-3 w-3\" />\n                <span>{game.server}</span>\n              </div>\n            )}\n            <div className=\"flex items-center gap-1\">\n              <Trophy className=\"h-3 w-3\" />\n              <span>{game.achievements.length} achievements</span>\n            </div>\n          </div>\n          \n          <div className=\"text-xs text-muted-foreground\">\n            {game.characters.length} character{game.characters.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n\n  const selectedGameData = games.find(game => game.gameId === selectedGame);\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold\">Games & Characters</h2>\n        {isOwnProfile && (\n          <Button variant=\"outline\" size=\"sm\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Add Game\n          </Button>\n        )}\n      </div>\n\n      <Tabs value=\"overview\" className=\"w-full\">\n        <TabsList>\n          <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n          <TabsTrigger value=\"characters\">Characters</TabsTrigger>\n        </TabsList>\n        \n        <TabsContent value=\"overview\" className=\"space-y-4\">\n          <div className=\"grid gap-4 md:grid-cols-2\">\n            {games.map((game) => (\n              <GameCard key={game.gameId} game={game} />\n            ))}\n          </div>\n        </TabsContent>\n        \n        <TabsContent value=\"characters\" className=\"space-y-4\">\n          {selectedGameData && (\n            <div>\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium\">{selectedGameData.gameName} Characters</h3>\n                {isOwnProfile && (\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Character\n                  </Button>\n                )}\n              </div>\n              \n              <div className=\"grid gap-4 md:grid-cols-2\">\n                {selectedGameData.characters.map((character) => (\n                  <CharacterCard \n                    key={character.id} \n                    character={character} \n                    game={selectedGameData} \n                  />\n                ))}\n              </div>\n            </div>\n          )}\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AA0BO,SAAS,gBAAgB,EAAE,KAAK,EAAE,eAAe,KAAK,EAAwB;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,CAAC,EAAE,EAAE,UAAU;IAE7E,MAAM,cAAc,CAAC;QACnB,IAAI,QAAQ,IAAI;YACd,OAAO,GAAG,MAAM,CAAC,CAAC;QACpB;QACA,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;QAChC,MAAM,iBAAiB,QAAQ;QAC/B,OAAO,GAAG,KAAK,EAAE,EAAE,eAAe,CAAC,CAAC;IACtC;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;IAEA,MAAM,mBAAmB,CAAC;QACxB,qCAAqC;QACrC,MAAM,aAA8C;YAClD,8BAAgB,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACjC,yBAAW,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAC7B,6BAAe,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAClC;QACA,OAAO,UAAU,CAAC,kBAAkB,GAAG,kBAAI,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAC7D;IAEA,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAE,IAAI,EAA+C,iBACrF,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,UAAU,MAAM;gDAAE,KAAK,UAAU,IAAI;;;;;;0DACvD,8OAAC,kIAAA,CAAA,iBAAc;0DACZ,iBAAiB,UAAU,KAAK;;;;;;;;;;;;kDAGrC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiB,UAAU,IAAI;;;;;;oDAC5C,UAAU,SAAS,kBAClB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;wDAA0B,OAAM;;;;;;;;;;;;0DAGrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAO,UAAU,KAAK;;;;;;;oDAC3B,UAAU,KAAK,kBACd;;0EACE,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,UAAU,KAAK;;;;;;;;oDAGzB,UAAU,IAAI,kBACb;;0EACE,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACtC,UAAU,KAAK;;;;;;;oCAErB,UAAU,SAAS,kBAClB,8OAAC;wCAAE,WAAU;;4CAAqC;4CAC1C,UAAU,SAAS;;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAI,WAAU;;4BACZ,UAAU,MAAM,kBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAM,UAAU,MAAM;;;;;;oCACtB,UAAU,OAAO,kBAChB;;0DACE,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,UAAU,OAAO;;;;;;;;;;;;;;4BAK/B,UAAU,cAAc,kBACvB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;IAQrC,MAAM,WAAW,CAAC,EAAE,IAAI,EAAyB,iBAC/C,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;YACV,SAAS,IAAM,gBAAgB,KAAK,MAAM;sBAC9C,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,KAAK,QAAQ;gDAAE,KAAK,KAAK,QAAQ;;;;;;0DACnD,8OAAC,kIAAA,CAAA,iBAAc;0DACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;kDAG1C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiB,KAAK,QAAQ;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAM,YAAY,KAAK,WAAW;;;;;;oDAClC,KAAK,KAAK,kBACT;;0EACE,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAK;oEAAO,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAW,iBAAiB,KAAK,QAAQ;wCAAG,SAAQ;kDACxD,KAAK,QAAQ,CAAC,WAAW;;;;;;oCAE3B,KAAK,IAAI,kBACR,8OAAC;wCAAE,WAAU;kDAAiC,KAAK,IAAI;;;;;;;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,kBACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,KAAK,MAAM;;;;;;;;;;;;kDAGtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;;oDAAM,KAAK,YAAY,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAIpC,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,UAAU,CAAC,MAAM;oCAAC;oCAAW,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;IAOlF,MAAM,mBAAmB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;oBACrC,8BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;;0CAC7B,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMvC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAM;gBAAW,WAAU;;kCAC/B,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;;;;;;;kCAGlC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAA2B,MAAM;mCAAnB,KAAK,MAAM;;;;;;;;;;;;;;;kCAKhC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;kCACvC,kCACC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAuB,iBAAiB,QAAQ;gDAAC;;;;;;;wCAC9D,8BACC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,UAAU,CAAC,GAAG,CAAC,CAAC,0BAChC,8OAAC;4CAEC,WAAW;4CACX,MAAM;2CAFD,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC", "debugId": null}}, {"offset": {"line": 2605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/profile/achievements.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';\nimport { Progress } from '@/components/ui/progress';\nimport { Achievement, GameAchievement } from '@/types/gamer';\nimport { \n  Trophy, \n  Star, \n  Crown, \n  Award,\n  Calendar,\n  Filter,\n  Gamepad2\n} from 'lucide-react';\n\ninterface AchievementsProps {\n  achievements: Achievement[];\n  recentAchievements?: Achievement[];\n}\n\nexport function Achievements({ achievements, recentAchievements = [] }: AchievementsProps) {\n  const [filter, setFilter] = useState<'all' | 'recent' | 'rare'>('all');\n\n  const getRarityColor = (rarity: Achievement['rarity']) => {\n    const colors = {\n      common: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n      uncommon: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n      rare: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      epic: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',\n      legendary: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    };\n    return colors[rarity];\n  };\n\n  const getRarityIcon = (rarity: Achievement['rarity']) => {\n    switch (rarity) {\n      case 'legendary':\n        return <Crown className=\"h-4 w-4\" />;\n      case 'epic':\n        return <Award className=\"h-4 w-4\" />;\n      case 'rare':\n        return <Star className=\"h-4 w-4\" />;\n      default:\n        return <Trophy className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getFilteredAchievements = () => {\n    switch (filter) {\n      case 'recent':\n        return recentAchievements;\n      case 'rare':\n        return achievements.filter(ach => ['rare', 'epic', 'legendary'].includes(ach.rarity));\n      default:\n        return achievements;\n    }\n  };\n\n  const AchievementCard = ({ achievement }: { achievement: Achievement }) => {\n    const isGameSpecific = 'gameSpecific' in achievement && achievement.gameSpecific;\n    const gameAchievement = achievement as GameAchievement;\n    \n    return (\n      <Card className=\"transition-all hover:shadow-md\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-start gap-3\">\n            <div className={`p-2 rounded-lg ${getRarityColor(achievement.rarity)}`}>\n              {achievement.icon ? (\n                <img src={achievement.icon} alt=\"\" className=\"h-6 w-6\" />\n              ) : (\n                getRarityIcon(achievement.rarity)\n              )}\n            </div>\n            \n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-start justify-between gap-2\">\n                <div>\n                  <h4 className=\"font-semibold text-sm leading-tight\">\n                    {achievement.title}\n                  </h4>\n                  <p className=\"text-xs text-muted-foreground mt-1 leading-relaxed\">\n                    {achievement.description}\n                  </p>\n                </div>\n                \n                <Badge \n                  className={`${getRarityColor(achievement.rarity)} text-xs shrink-0`}\n                  variant=\"secondary\"\n                >\n                  {achievement.rarity}\n                </Badge>\n              </div>\n              \n              <div className=\"flex items-center justify-between mt-3\">\n                <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                  <Calendar className=\"h-3 w-3\" />\n                  <span>{achievement.unlockedAt.toLocaleDateString()}</span>\n                  {isGameSpecific && gameAchievement.category && (\n                    <>\n                      <span>•</span>\n                      <span>{gameAchievement.category}</span>\n                    </>\n                  )}\n                </div>\n                \n                {achievement.gameId && (\n                  <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                    <Gamepad2 className=\"h-3 w-3\" />\n                    <span>Game Achievement</span>\n                  </div>\n                )}\n              </div>\n              \n              {achievement.progress !== undefined && achievement.maxProgress && (\n                <div className=\"mt-2\">\n                  <div className=\"flex justify-between text-xs text-muted-foreground mb-1\">\n                    <span>Progress</span>\n                    <span>{achievement.progress}/{achievement.maxProgress}</span>\n                  </div>\n                  <Progress \n                    value={(achievement.progress / achievement.maxProgress) * 100} \n                    className=\"h-1\"\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  };\n\n  const getRarityStats = () => {\n    const stats = achievements.reduce((acc, ach) => {\n      acc[ach.rarity] = (acc[ach.rarity] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n    \n    return stats;\n  };\n\n  const rarityStats = getRarityStats();\n  const filteredAchievements = getFilteredAchievements();\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold\">Achievements</h2>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant={filter === 'all' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setFilter('all')}\n          >\n            All ({achievements.length})\n          </Button>\n          <Button\n            variant={filter === 'recent' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setFilter('recent')}\n          >\n            Recent ({recentAchievements.length})\n          </Button>\n          <Button\n            variant={filter === 'rare' ? 'default' : 'outline'}\n            size=\"sm\"\n            onClick={() => setFilter('rare')}\n          >\n            <Filter className=\"h-4 w-4 mr-1\" />\n            Rare+\n          </Button>\n        </div>\n      </div>\n\n      {/* Achievement Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n        {Object.entries(rarityStats).map(([rarity, count]) => (\n          <Card key={rarity}>\n            <CardContent className=\"p-3 text-center\">\n              <div className={`inline-flex p-2 rounded-lg ${getRarityColor(rarity as Achievement['rarity'])}`}>\n                {getRarityIcon(rarity as Achievement['rarity'])}\n              </div>\n              <div className=\"mt-2\">\n                <div className=\"text-lg font-bold\">{count}</div>\n                <div className=\"text-xs text-muted-foreground capitalize\">{rarity}</div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Recent Achievements Highlight */}\n      {recentAchievements.length > 0 && filter === 'all' && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg flex items-center gap-2\">\n              <Star className=\"h-5 w-5 text-yellow-500\" />\n              Recent Achievements\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid gap-3 md:grid-cols-2\">\n              {recentAchievements.slice(0, 4).map((achievement) => (\n                <div key={achievement.id} className=\"flex items-center gap-3 p-2 rounded-lg bg-muted/50\">\n                  <div className={`p-1.5 rounded ${getRarityColor(achievement.rarity)}`}>\n                    {getRarityIcon(achievement.rarity)}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"font-medium text-sm truncate\">{achievement.title}</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {achievement.unlockedAt.toLocaleDateString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Achievement Grid */}\n      <div className=\"grid gap-4 md:grid-cols-2\">\n        {filteredAchievements.map((achievement) => (\n          <AchievementCard key={achievement.id} achievement={achievement} />\n        ))}\n      </div>\n\n      {filteredAchievements.length === 0 && (\n        <Card>\n          <CardContent className=\"p-8 text-center\">\n            <Trophy className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium mb-2\">No achievements found</h3>\n            <p className=\"text-muted-foreground\">\n              {filter === 'recent' \n                ? \"No recent achievements to display.\"\n                : filter === 'rare'\n                ? \"No rare achievements unlocked yet.\"\n                : \"Start playing games to unlock achievements!\"\n              }\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;AAwBO,SAAS,aAAa,EAAE,YAAY,EAAE,qBAAqB,EAAE,EAAqB;IACvF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAEhE,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,MAAM;YACN,MAAM;YACN,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAAO;IACvB;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,0BAA0B;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,aAAa,MAAM,CAAC,CAAA,MAAO;wBAAC;wBAAQ;wBAAQ;qBAAY,CAAC,QAAQ,CAAC,IAAI,MAAM;YACrF;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC,EAAE,WAAW,EAAgC;QACpE,MAAM,iBAAiB,kBAAkB,eAAe,YAAY,YAAY;QAChF,MAAM,kBAAkB;QAExB,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAW,CAAC,eAAe,EAAE,eAAe,YAAY,MAAM,GAAG;sCACnE,YAAY,IAAI,iBACf,8OAAC;gCAAI,KAAK,YAAY,IAAI;gCAAE,KAAI;gCAAG,WAAU;;;;;uCAE7C,cAAc,YAAY,MAAM;;;;;;sCAIpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,YAAY,KAAK;;;;;;8DAEpB,8OAAC;oDAAE,WAAU;8DACV,YAAY,WAAW;;;;;;;;;;;;sDAI5B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,WAAW,GAAG,eAAe,YAAY,MAAM,EAAE,iBAAiB,CAAC;4CACnE,SAAQ;sDAEP,YAAY,MAAM;;;;;;;;;;;;8CAIvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAM,YAAY,UAAU,CAAC,kBAAkB;;;;;;gDAC/C,kBAAkB,gBAAgB,QAAQ,kBACzC;;sEACE,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,gBAAgB,QAAQ;;;;;;;;;;;;;;wCAKpC,YAAY,MAAM,kBACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;gCAKX,YAAY,QAAQ,KAAK,aAAa,YAAY,WAAW,kBAC5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,YAAY,QAAQ;wDAAC;wDAAE,YAAY,WAAW;;;;;;;;;;;;;sDAEvD,8OAAC,oIAAA,CAAA,WAAQ;4CACP,OAAO,AAAC,YAAY,QAAQ,GAAG,YAAY,WAAW,GAAI;4CAC1D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS5B;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,aAAa,MAAM,CAAC,CAAC,KAAK;YACtC,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;YAC3C,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO;IACT;IAEA,MAAM,cAAc;IACpB,MAAM,uBAAuB;IAE7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,WAAW,QAAQ,YAAY;gCACxC,MAAK;gCACL,SAAS,IAAM,UAAU;;oCAC1B;oCACO,aAAa,MAAM;oCAAC;;;;;;;0CAE5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,WAAW,WAAW,YAAY;gCAC3C,MAAK;gCACL,SAAS,IAAM,UAAU;;oCAC1B;oCACU,mBAAmB,MAAM;oCAAC;;;;;;;0CAErC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,WAAW,SAAS,YAAY;gCACzC,MAAK;gCACL,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,iBAC/C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAW,CAAC,2BAA2B,EAAE,eAAe,SAAkC;8CAC5F,cAAc;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;uBAPtD;;;;;;;;;;YAed,mBAAmB,MAAM,GAAG,KAAK,WAAW,uBAC3C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAIhD,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BACnC,8OAAC;oCAAyB,WAAU;;sDAClC,8OAAC;4CAAI,WAAW,CAAC,cAAc,EAAE,eAAe,YAAY,MAAM,GAAG;sDAClE,cAAc,YAAY,MAAM;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAgC,YAAY,KAAK;;;;;;8DAC9D,8OAAC;oDAAE,WAAU;8DACV,YAAY,UAAU,CAAC,kBAAkB;;;;;;;;;;;;;mCAPtC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;0BAkBlC,8OAAC;gBAAI,WAAU;0BACZ,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC;wBAAqC,aAAa;uBAA7B,YAAY,EAAE;;;;;;;;;;YAIvC,qBAAqB,MAAM,KAAK,mBAC/B,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,8OAAC;4BAAE,WAAU;sCACV,WAAW,WACR,uCACA,WAAW,SACX,uCACA;;;;;;;;;;;;;;;;;;;;;;;AAQlB", "debugId": null}}, {"offset": {"line": 3191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/components/profile/profile-skeleton.tsx"], "sourcesContent": ["import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';\nimport { Skeleton } from '@/components/ui/skeleton';\n\nexport function ProfileSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      {/* Profile Header Skeleton */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col md:flex-row gap-6\">\n            <div className=\"flex flex-col items-center md:items-start\">\n              <Skeleton className=\"h-24 w-24 md:h-32 md:w-32 rounded-full\" />\n              <Skeleton className=\"h-4 w-16 mt-2\" />\n            </div>\n            \n            <div className=\"flex-1 space-y-4\">\n              <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n                <div>\n                  <Skeleton className=\"h-8 w-48\" />\n                  <Skeleton className=\"h-4 w-32 mt-2\" />\n                </div>\n                <Skeleton className=\"h-9 w-28\" />\n              </div>\n              \n              <Skeleton className=\"h-16 w-full\" />\n              \n              <div className=\"flex flex-wrap gap-4\">\n                <Skeleton className=\"h-4 w-24\" />\n                <Skeleton className=\"h-4 w-20\" />\n                <Skeleton className=\"h-4 w-32\" />\n              </div>\n              \n              <div className=\"flex flex-wrap gap-2\">\n                <Skeleton className=\"h-6 w-20\" />\n                <Skeleton className=\"h-6 w-16\" />\n                <Skeleton className=\"h-6 w-24\" />\n                <Skeleton className=\"h-6 w-18\" />\n              </div>\n              \n              <div>\n                <Skeleton className=\"h-4 w-24 mb-2\" />\n                <div className=\"flex flex-wrap gap-1\">\n                  <Skeleton className=\"h-5 w-12\" />\n                  <Skeleton className=\"h-5 w-16\" />\n                  <Skeleton className=\"h-5 w-14\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Tabs Skeleton */}\n      <div className=\"space-y-4\">\n        <div className=\"flex space-x-1 bg-muted p-1 rounded-lg\">\n          <Skeleton className=\"h-8 w-20\" />\n          <Skeleton className=\"h-8 w-16\" />\n          <Skeleton className=\"h-8 w-24\" />\n          <Skeleton className=\"h-8 w-16\" />\n        </div>\n        \n        {/* Stats Grid Skeleton */}\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {Array.from({ length: 6 }).map((_, i) => (\n            <Card key={i}>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <Skeleton className=\"h-4 w-24\" />\n                <Skeleton className=\"h-4 w-4\" />\n              </CardHeader>\n              <CardContent>\n                <Skeleton className=\"h-8 w-16 mb-2\" />\n                <Skeleton className=\"h-2 w-full mb-1\" />\n                <Skeleton className=\"h-3 w-20\" />\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function GameCardSkeleton() {\n  return (\n    <Card>\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <Skeleton className=\"h-12 w-12 rounded-full\" />\n            <div>\n              <Skeleton className=\"h-5 w-32 mb-2\" />\n              <Skeleton className=\"h-4 w-24\" />\n            </div>\n          </div>\n          <div className=\"text-right space-y-1\">\n            <Skeleton className=\"h-5 w-16\" />\n            <Skeleton className=\"h-3 w-12\" />\n          </div>\n        </div>\n        \n        <div className=\"mt-3 flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <Skeleton className=\"h-3 w-20\" />\n            <Skeleton className=\"h-3 w-24\" />\n          </div>\n          <Skeleton className=\"h-3 w-16\" />\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport function AchievementCardSkeleton() {\n  return (\n    <Card>\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start gap-3\">\n          <Skeleton className=\"h-10 w-10 rounded-lg\" />\n          <div className=\"flex-1\">\n            <div className=\"flex items-start justify-between gap-2\">\n              <div className=\"flex-1\">\n                <Skeleton className=\"h-4 w-40 mb-2\" />\n                <Skeleton className=\"h-3 w-full mb-1\" />\n                <Skeleton className=\"h-3 w-3/4\" />\n              </div>\n              <Skeleton className=\"h-5 w-12\" />\n            </div>\n            \n            <div className=\"flex items-center justify-between mt-3\">\n              <Skeleton className=\"h-3 w-24\" />\n              <Skeleton className=\"h-3 w-20\" />\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAGtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAEtB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAGtB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDAEpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAGtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAGtB,8OAAC;;0DACC,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BARb;;;;;;;;;;;;;;;;;;;;;;AAgBvB;AAEO,SAAS;IACd,qBACE,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;;sDACC,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAGxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAIxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEO,SAAS;IACd,qBACE,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAGtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 3800, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/contexts/profile-context.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, ReactNode } from 'react';\nimport { GamerProfile, Achievement } from '@/types/gamer';\n\ninterface ProfileState {\n  profile: GamerProfile | null;\n  recentAchievements: Achievement[];\n  loading: boolean;\n  error: string | null;\n  isEditing: boolean;\n}\n\ntype ProfileAction =\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_PROFILE'; payload: GamerProfile }\n  | { type: 'SET_RECENT_ACHIEVEMENTS'; payload: Achievement[] }\n  | { type: 'SET_ERROR'; payload: string | null }\n  | { type: 'SET_EDITING'; payload: boolean }\n  | { type: 'UPDATE_PROFILE'; payload: Partial<GamerProfile> }\n  | { type: 'ADD_ACHIEVEMENT'; payload: Achievement }\n  | { type: 'RESET' };\n\nconst initialState: ProfileState = {\n  profile: null,\n  recentAchievements: [],\n  loading: false,\n  error: null,\n  isEditing: false,\n};\n\nfunction profileReducer(state: ProfileState, action: ProfileAction): ProfileState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, loading: action.payload };\n    \n    case 'SET_PROFILE':\n      return { ...state, profile: action.payload, error: null };\n    \n    case 'SET_RECENT_ACHIEVEMENTS':\n      return { ...state, recentAchievements: action.payload };\n    \n    case 'SET_ERROR':\n      return { ...state, error: action.payload, loading: false };\n    \n    case 'SET_EDITING':\n      return { ...state, isEditing: action.payload };\n    \n    case 'UPDATE_PROFILE':\n      if (!state.profile) return state;\n      return {\n        ...state,\n        profile: { ...state.profile, ...action.payload },\n        isEditing: false,\n      };\n    \n    case 'ADD_ACHIEVEMENT':\n      return {\n        ...state,\n        recentAchievements: [action.payload, ...state.recentAchievements.slice(0, 9)],\n        profile: state.profile ? {\n          ...state.profile,\n          achievements: [action.payload, ...state.profile.achievements],\n          stats: {\n            ...state.profile.stats,\n            achievementsUnlocked: state.profile.stats.achievementsUnlocked + 1,\n          },\n        } : null,\n      };\n    \n    case 'RESET':\n      return initialState;\n    \n    default:\n      return state;\n  }\n}\n\ninterface ProfileContextType {\n  state: ProfileState;\n  dispatch: React.Dispatch<ProfileAction>;\n  actions: {\n    setLoading: (loading: boolean) => void;\n    setProfile: (profile: GamerProfile) => void;\n    setRecentAchievements: (achievements: Achievement[]) => void;\n    setError: (error: string | null) => void;\n    setEditing: (editing: boolean) => void;\n    updateProfile: (updates: Partial<GamerProfile>) => void;\n    addAchievement: (achievement: Achievement) => void;\n    reset: () => void;\n  };\n}\n\nconst ProfileContext = createContext<ProfileContextType | undefined>(undefined);\n\nexport function ProfileProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(profileReducer, initialState);\n\n  const actions = {\n    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),\n    setProfile: (profile: GamerProfile) => dispatch({ type: 'SET_PROFILE', payload: profile }),\n    setRecentAchievements: (achievements: Achievement[]) => \n      dispatch({ type: 'SET_RECENT_ACHIEVEMENTS', payload: achievements }),\n    setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),\n    setEditing: (editing: boolean) => dispatch({ type: 'SET_EDITING', payload: editing }),\n    updateProfile: (updates: Partial<GamerProfile>) => \n      dispatch({ type: 'UPDATE_PROFILE', payload: updates }),\n    addAchievement: (achievement: Achievement) => \n      dispatch({ type: 'ADD_ACHIEVEMENT', payload: achievement }),\n    reset: () => dispatch({ type: 'RESET' }),\n  };\n\n  return (\n    <ProfileContext.Provider value={{ state, dispatch, actions }}>\n      {children}\n    </ProfileContext.Provider>\n  );\n}\n\nexport function useProfile() {\n  const context = useContext(ProfileContext);\n  if (context === undefined) {\n    throw new Error('useProfile must be used within a ProfileProvider');\n  }\n  return context;\n}\n\n// Custom hooks for specific profile operations\nexport function useProfileActions() {\n  const { actions } = useProfile();\n  return actions;\n}\n\nexport function useProfileState() {\n  const { state } = useProfile();\n  return state;\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAuBA,MAAM,eAA6B;IACjC,SAAS;IACT,oBAAoB,EAAE;IACtB,SAAS;IACT,OAAO;IACP,WAAW;AACb;AAEA,SAAS,eAAe,KAAmB,EAAE,MAAqB;IAChE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;YAAC;QAE7C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;gBAAE,OAAO;YAAK;QAE1D,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,oBAAoB,OAAO,OAAO;YAAC;QAExD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;gBAAE,SAAS;YAAM;QAE3D,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAE/C,KAAK;YACH,IAAI,CAAC,MAAM,OAAO,EAAE,OAAO;YAC3B,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,OAAO,OAAO;gBAAC;gBAC/C,WAAW;YACb;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,oBAAoB;oBAAC,OAAO,OAAO;uBAAK,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG;iBAAG;gBAC7E,SAAS,MAAM,OAAO,GAAG;oBACvB,GAAG,MAAM,OAAO;oBAChB,cAAc;wBAAC,OAAO,OAAO;2BAAK,MAAM,OAAO,CAAC,YAAY;qBAAC;oBAC7D,OAAO;wBACL,GAAG,MAAM,OAAO,CAAC,KAAK;wBACtB,sBAAsB,MAAM,OAAO,CAAC,KAAK,CAAC,oBAAoB,GAAG;oBACnE;gBACF,IAAI;YACN;QAEF,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAiBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB;IAErD,MAAM,UAAU;QACd,YAAY,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAQ;QACnF,YAAY,CAAC,UAA0B,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAQ;QACxF,uBAAuB,CAAC,eACtB,SAAS;gBAAE,MAAM;gBAA2B,SAAS;YAAa;QACpE,UAAU,CAAC,QAAyB,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAAM;QACjF,YAAY,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAQ;QACnF,eAAe,CAAC,UACd,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAQ;QACtD,gBAAgB,CAAC,cACf,SAAS;gBAAE,MAAM;gBAAmB,SAAS;YAAY;QAC3D,OAAO,IAAM,SAAS;gBAAE,MAAM;YAAQ;IACxC;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAQ;kBACxD;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,OAAO;AACT;AAEO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3950, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/lib/mock-data.ts"], "sourcesContent": ["import { GamerProfile, GameProfile, Character, Achievement } from '@/types/gamer';\n\nexport const mockGamerProfile: GamerProfile = {\n  id: 'user-123',\n  username: 'shadowhunter92',\n  displayName: 'ShadowHunter',\n  avatar: '/avatars/shadow-hunter.jpg',\n  bio: 'Passionate MMORPG player with 10+ years of experience. Love raiding, PvP, and helping new players. Always looking for active guilds!',\n  location: 'San Francisco, CA',\n  timezone: 'PST',\n  joinDate: new Date('2020-03-15'),\n  isOnline: true,\n  lastSeen: new Date(),\n  \n  playStyle: 'mixed',\n  preferredRoles: ['Tank', 'DPS', 'Support'],\n  availableHours: {\n    monday: [{ start: '19:00', end: '23:00' }],\n    tuesday: [{ start: '19:00', end: '23:00' }],\n    wednesday: [{ start: '19:00', end: '23:00' }],\n    thursday: [{ start: '19:00', end: '23:00' }],\n    friday: [{ start: '18:00', end: '02:00' }],\n    saturday: [{ start: '14:00', end: '02:00' }],\n    sunday: [{ start: '14:00', end: '23:00' }],\n  },\n  \n  games: [\n    {\n      gameId: 'wow',\n      gameName: 'World of Warcraft',\n      gameIcon: '/game-icons/wow.png',\n      platform: 'battlenet',\n      server: 'Stormrage',\n      region: 'US',\n      characters: [\n        {\n          id: 'char-1',\n          name: 'Shadowmend',\n          class: 'Death Knight',\n          race: 'Human',\n          level: 80,\n          server: 'Stormrage',\n          faction: 'Alliance',\n          specialization: 'Blood',\n          itemLevel: 485,\n          isPrimary: true,\n        },\n        {\n          id: 'char-2',\n          name: 'Lightbringer',\n          class: 'Paladin',\n          race: 'Dwarf',\n          level: 75,\n          server: 'Stormrage',\n          faction: 'Alliance',\n          specialization: 'Protection',\n          itemLevel: 450,\n          isPrimary: false,\n        },\n      ],\n      hoursPlayed: 2847,\n      level: 80,\n      rank: 'Mythic Raider',\n      achievements: [\n        {\n          id: 'wow-ach-1',\n          title: 'Cutting Edge: Fyrakk',\n          description: 'Defeat Fyrakk the Blazing on Mythic difficulty',\n          rarity: 'legendary',\n          unlockedAt: new Date('2024-01-15'),\n          gameSpecific: true,\n          category: 'Raids',\n        },\n      ],\n      startedPlaying: new Date('2019-08-20'),\n      lastPlayed: new Date(),\n    },\n    {\n      gameId: 'ff14',\n      gameName: 'Final Fantasy XIV',\n      gameIcon: '/game-icons/ff14.png',\n      platform: 'steam',\n      server: 'Gilgamesh',\n      region: 'NA',\n      characters: [\n        {\n          id: 'char-3',\n          name: 'Kira Nightfall',\n          class: 'Dark Knight',\n          race: 'Au Ra',\n          level: 90,\n          server: 'Gilgamesh',\n          specialization: 'Tank',\n          itemLevel: 630,\n          isPrimary: true,\n        },\n      ],\n      hoursPlayed: 1250,\n      level: 90,\n      achievements: [],\n      startedPlaying: new Date('2021-07-01'),\n      lastPlayed: new Date('2024-12-10'),\n    },\n  ],\n  \n  achievements: [\n    {\n      id: 'global-ach-1',\n      title: 'Guild Master',\n      description: 'Successfully lead a guild for 6 months',\n      rarity: 'rare',\n      unlockedAt: new Date('2023-06-15'),\n    },\n    {\n      id: 'global-ach-2',\n      title: 'Veteran Player',\n      description: 'Play for over 1000 hours across all games',\n      rarity: 'uncommon',\n      unlockedAt: new Date('2022-11-20'),\n    },\n  ],\n  \n  friends: ['user-456', 'user-789'],\n  guilds: [\n    {\n      guildId: 'guild-1',\n      guildName: 'Shadow Legends',\n      guildTag: 'SHDW',\n      role: 'officer',\n      joinedAt: new Date('2023-01-10'),\n      gameId: 'wow',\n      isActive: true,\n    },\n    {\n      guildId: 'guild-2',\n      guildName: 'Crystal Guardians',\n      role: 'member',\n      joinedAt: new Date('2023-08-15'),\n      gameId: 'ff14',\n      isActive: true,\n    },\n  ],\n  \n  privacy: {\n    profileVisibility: 'public',\n    showOnlineStatus: true,\n    showGameActivity: true,\n    showAchievements: true,\n    showStats: true,\n    allowFriendRequests: true,\n    allowGuildInvites: true,\n  },\n  \n  stats: {\n    totalHoursPlayed: 4097,\n    gamesOwned: 2,\n    achievementsUnlocked: 47,\n    guildsJoined: 5,\n    friendsCount: 23,\n    favoriteGame: 'World of Warcraft',\n    mostPlayedGenre: 'mmorpg',\n    averageSessionLength: 180, // 3 hours\n  },\n};\n\nexport const mockRecentAchievements: Achievement[] = [\n  {\n    id: 'recent-1',\n    title: 'Mythic+ Master',\n    description: 'Complete 100 Mythic+ dungeons',\n    rarity: 'epic',\n    unlockedAt: new Date('2024-12-14'),\n    gameId: 'wow',\n  },\n  {\n    id: 'recent-2',\n    title: 'Social Butterfly',\n    description: 'Add 20 friends to your network',\n    rarity: 'common',\n    unlockedAt: new Date('2024-12-12'),\n  },\n  {\n    id: 'recent-3',\n    title: 'Raid Leader',\n    description: 'Successfully lead 10 raid groups',\n    rarity: 'rare',\n    unlockedAt: new Date('2024-12-08'),\n    gameId: 'wow',\n  },\n];\n\n// Helper function to simulate API delay\nexport const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Mock API functions\nexport const fetchGamerProfile = async (userId: string): Promise<GamerProfile> => {\n  await delay(1000); // Simulate network delay\n  return mockGamerProfile;\n};\n\nexport const updateGamerProfile = async (userId: string, updates: Partial<GamerProfile>): Promise<GamerProfile> => {\n  await delay(800);\n  return { ...mockGamerProfile, ...updates };\n};\n\nexport const fetchRecentAchievements = async (userId: string): Promise<Achievement[]> => {\n  await delay(600);\n  return mockRecentAchievements;\n};\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,mBAAiC;IAC5C,IAAI;IACJ,UAAU;IACV,aAAa;IACb,QAAQ;IACR,KAAK;IACL,UAAU;IACV,UAAU;IACV,UAAU,IAAI,KAAK;IACnB,UAAU;IACV,UAAU,IAAI;IAEd,WAAW;IACX,gBAAgB;QAAC;QAAQ;QAAO;KAAU;IAC1C,gBAAgB;QACd,QAAQ;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;QAC1C,SAAS;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;QAC3C,WAAW;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;QAC7C,UAAU;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;QAC5C,QAAQ;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;QAC1C,UAAU;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;QAC5C,QAAQ;YAAC;gBAAE,OAAO;gBAAS,KAAK;YAAQ;SAAE;IAC5C;IAEA,OAAO;QACL;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,gBAAgB;oBAChB,WAAW;oBACX,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,gBAAgB;oBAChB,WAAW;oBACX,WAAW;gBACb;aACD;YACD,aAAa;YACb,OAAO;YACP,MAAM;YACN,cAAc;gBACZ;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,QAAQ;oBACR,YAAY,IAAI,KAAK;oBACrB,cAAc;oBACd,UAAU;gBACZ;aACD;YACD,gBAAgB,IAAI,KAAK;YACzB,YAAY,IAAI;QAClB;QACA;YACE,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,YAAY;gBACV;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,gBAAgB;oBAChB,WAAW;oBACX,WAAW;gBACb;aACD;YACD,aAAa;YACb,OAAO;YACP,cAAc,EAAE;YAChB,gBAAgB,IAAI,KAAK;YACzB,YAAY,IAAI,KAAK;QACvB;KACD;IAED,cAAc;QACZ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY,IAAI,KAAK;QACvB;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,YAAY,IAAI,KAAK;QACvB;KACD;IAED,SAAS;QAAC;QAAY;KAAW;IACjC,QAAQ;QACN;YACE,SAAS;YACT,WAAW;YACX,UAAU;YACV,MAAM;YACN,UAAU,IAAI,KAAK;YACnB,QAAQ;YACR,UAAU;QACZ;QACA;YACE,SAAS;YACT,WAAW;YACX,MAAM;YACN,UAAU,IAAI,KAAK;YACnB,QAAQ;YACR,UAAU;QACZ;KACD;IAED,SAAS;QACP,mBAAmB;QACnB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,WAAW;QACX,qBAAqB;QACrB,mBAAmB;IACrB;IAEA,OAAO;QACL,kBAAkB;QAClB,YAAY;QACZ,sBAAsB;QACtB,cAAc;QACd,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,sBAAsB;IACxB;AACF;AAEO,MAAM,yBAAwC;IACnD;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,YAAY,IAAI,KAAK;IACvB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,YAAY,IAAI,KAAK;QACrB,QAAQ;IACV;CACD;AAGM,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAGzE,MAAM,oBAAoB,OAAO;IACtC,MAAM,MAAM,OAAO,yBAAyB;IAC5C,OAAO;AACT;AAEO,MAAM,qBAAqB,OAAO,QAAgB;IACvD,MAAM,MAAM;IACZ,OAAO;QAAE,GAAG,gBAAgB;QAAE,GAAG,OAAO;IAAC;AAC3C;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,MAAM;IACZ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4201, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/www/guildie/src/app/profile/%5Busername%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Separator } from '@/components/ui/separator';\nimport { ProfileHeader } from '@/components/profile/profile-header';\nimport { GamingStats } from '@/components/profile/gaming-stats';\nimport { GamesCharacters } from '@/components/profile/games-characters';\nimport { Achievements } from '@/components/profile/achievements';\nimport { ProfileSkeleton } from '@/components/profile/profile-skeleton';\nimport { ProfileProvider, useProfile, useProfileActions } from '@/contexts/profile-context';\nimport { GamerProfile } from '@/types/gamer';\nimport { fetchGamerProfile, fetchRecentAchievements, updateGamerProfile } from '@/lib/mock-data';\nimport {\n  Loader2,\n  AlertCircle,\n  UserPlus,\n  MessageCircle,\n  Share,\n  MoreHorizontal,\n  Users,\n  Calendar,\n  RefreshCw\n} from 'lucide-react';\n\nfunction ProfilePageContent() {\n  const params = useParams();\n  const username = params.username as string;\n  const { state } = useProfile();\n  const actions = useProfileActions();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Mock check if this is the current user's profile\n  const isOwnProfile = username === 'shadowhunter92';\n\n  useEffect(() => {\n    const loadProfile = async () => {\n      try {\n        actions.setLoading(true);\n        actions.setError(null);\n\n        const [profileData, achievementsData] = await Promise.all([\n          fetchGamerProfile(username),\n          fetchRecentAchievements(username)\n        ]);\n\n        actions.setProfile(profileData);\n        actions.setRecentAchievements(achievementsData);\n      } catch (err) {\n        actions.setError('Failed to load profile. Please try again.');\n        console.error('Error loading profile:', err);\n      } finally {\n        actions.setLoading(false);\n      }\n    };\n\n    if (username) {\n      loadProfile();\n    }\n\n    return () => {\n      actions.reset();\n    };\n  }, [username, actions]);\n\n  const handleProfileUpdate = async (updates: Partial<GamerProfile>) => {\n    if (!state.profile) return;\n\n    try {\n      actions.setLoading(true);\n      const updatedProfile = await updateGamerProfile(state.profile.id, updates);\n      actions.updateProfile(updates);\n    } catch (err) {\n      actions.setError('Failed to update profile. Please try again.');\n      console.error('Error updating profile:', err);\n    } finally {\n      actions.setLoading(false);\n    }\n  };\n\n  const handleRetry = () => {\n    window.location.reload();\n  };\n\n  if (state.loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n          <div className=\"container mx-auto px-4 py-3\">\n            <div className=\"flex items-center gap-4\">\n              <h1 className=\"text-xl font-bold\">Guildie</h1>\n              <Separator orientation=\"vertical\" className=\"h-6\" />\n              <span className=\"text-muted-foreground\">Profile</span>\n            </div>\n          </div>\n        </nav>\n        <main className=\"container mx-auto px-4 py-6 max-w-6xl\">\n          <ProfileSkeleton />\n        </main>\n      </div>\n    );\n  }\n\n  if (state.error || !state.profile) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"p-6 text-center\">\n            <AlertCircle className=\"h-12 w-12 text-destructive mx-auto mb-4\" />\n            <h2 className=\"text-lg font-semibold mb-2\">Profile Not Found</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              {state.error || \"The profile you're looking for doesn't exist or has been removed.\"}\n            </p>\n            <div className=\"flex gap-2 justify-center\">\n              <Button onClick={handleRetry}>\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Try Again\n              </Button>\n              <Button variant=\"outline\" onClick={() => window.history.back()}>\n                Go Back\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Navigation Bar */}\n      <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container mx-auto px-4 py-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <h1 className=\"text-xl font-bold\">Guildie</h1>\n              <Separator orientation=\"vertical\" className=\"h-6\" />\n              <span className=\"text-muted-foreground\">Profile</span>\n            </div>\n            \n            {!isOwnProfile && (\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <UserPlus className=\"h-4 w-4 mr-2\" />\n                  Add Friend\n                </Button>\n                <Button variant=\"outline\" size=\"sm\">\n                  <MessageCircle className=\"h-4 w-4 mr-2\" />\n                  Message\n                </Button>\n                <Button variant=\"outline\" size=\"sm\">\n                  <Share className=\"h-4 w-4 mr-2\" />\n                  Share\n                </Button>\n                <Button variant=\"ghost\" size=\"sm\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-6 max-w-6xl\">\n        <div className=\"space-y-6\">\n          {/* Profile Header */}\n          <ProfileHeader\n            profile={state.profile}\n            isOwnProfile={isOwnProfile}\n            onProfileUpdate={handleProfileUpdate}\n          />\n\n          {/* Profile Content Tabs */}\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n              <TabsTrigger value=\"games\">Games</TabsTrigger>\n              <TabsTrigger value=\"achievements\">Achievements</TabsTrigger>\n              <TabsTrigger value=\"social\">Social</TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"overview\" className=\"space-y-6 mt-6\">\n              {/* Gaming Statistics */}\n              <div>\n                <h2 className=\"text-xl font-semibold mb-4\">Gaming Statistics</h2>\n                <GamingStats stats={state.profile.stats} />\n              </div>\n              \n              <Separator />\n              \n              {/* Recent Activity */}\n              <div>\n                <h2 className=\"text-xl font-semibold mb-4\">Recent Activity</h2>\n                <div className=\"grid gap-4 md:grid-cols-2\">\n                  <Card>\n                    <CardContent className=\"p-4\">\n                      <h3 className=\"font-medium mb-2\">Recent Achievements</h3>\n                      <div className=\"space-y-2\">\n                        {state.recentAchievements.slice(0, 3).map((achievement) => (\n                          <div key={achievement.id} className=\"flex items-center gap-2 text-sm\">\n                            <div className=\"h-2 w-2 bg-primary rounded-full\" />\n                            <span className=\"truncate\">{achievement.title}</span>\n                            <span className=\"text-muted-foreground text-xs\">\n                              {achievement.unlockedAt.toLocaleDateString()}\n                            </span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                  \n                  <Card>\n                    <CardContent className=\"p-4\">\n                      <h3 className=\"font-medium mb-2\">Active Guilds</h3>\n                      <div className=\"space-y-2\">\n                        {state.profile.guilds.filter(g => g.isActive).map((guild) => (\n                          <div key={guild.guildId} className=\"flex items-center gap-2 text-sm\">\n                            <Users className=\"h-3 w-3 text-muted-foreground\" />\n                            <span className=\"truncate\">{guild.guildName}</span>\n                            <span className=\"text-muted-foreground text-xs\">\n                              {guild.role}\n                            </span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                </div>\n              </div>\n            </TabsContent>\n            \n            <TabsContent value=\"games\" className=\"mt-6\">\n              <GamesCharacters\n                games={state.profile.games}\n                isOwnProfile={isOwnProfile}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"achievements\" className=\"mt-6\">\n              <Achievements\n                achievements={state.profile.achievements}\n                recentAchievements={state.recentAchievements}\n              />\n            </TabsContent>\n            \n            <TabsContent value=\"social\" className=\"mt-6\">\n              <div className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-xl font-semibold mb-4\">Guild Memberships</h2>\n                  <div className=\"grid gap-4 md:grid-cols-2\">\n                    {state.profile.guilds.map((guild) => (\n                      <Card key={guild.guildId}>\n                        <CardContent className=\"p-4\">\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <h3 className=\"font-semibold\">{guild.guildName}</h3>\n                              {guild.guildTag && (\n                                <p className=\"text-sm text-muted-foreground\">[{guild.guildTag}]</p>\n                              )}\n                            </div>\n                            <div className=\"text-right\">\n                              <p className=\"text-sm font-medium capitalize\">{guild.role}</p>\n                              <p className=\"text-xs text-muted-foreground\">\n                                {guild.isActive ? 'Active' : 'Inactive'}\n                              </p>\n                            </div>\n                          </div>\n                          <div className=\"mt-2 flex items-center gap-1 text-xs text-muted-foreground\">\n                            <Calendar className=\"h-3 w-3\" />\n                            <span>Joined {guild.joinedAt.toLocaleDateString()}</span>\n                          </div>\n                        </CardContent>\n                      </Card>\n                    ))}\n                  </div>\n                </div>\n                \n                <div>\n                  <h2 className=\"text-xl font-semibold mb-4\">Gaming Network</h2>\n                  <div className=\"grid gap-4 md:grid-cols-3\">\n                    <Card>\n                      <CardContent className=\"p-4 text-center\">\n                        <div className=\"text-2xl font-bold\">{state.profile.stats.friendsCount}</div>\n                        <div className=\"text-sm text-muted-foreground\">Friends</div>\n                      </CardContent>\n                    </Card>\n                    <Card>\n                      <CardContent className=\"p-4 text-center\">\n                        <div className=\"text-2xl font-bold\">{state.profile.guilds.length}</div>\n                        <div className=\"text-sm text-muted-foreground\">Guilds Joined</div>\n                      </CardContent>\n                    </Card>\n                    <Card>\n                      <CardContent className=\"p-4 text-center\">\n                        <div className=\"text-2xl font-bold\">\n                          {state.profile.guilds.filter(g => g.isActive).length}\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">Active Guilds</div>\n                      </CardContent>\n                    </Card>\n                  </div>\n                </div>\n              </div>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default function ProfilePage() {\n  return (\n    <ProfileProvider>\n      <ProfilePageContent />\n    </ProfileProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;;;;;;;AA4BA,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,QAAQ;IAChC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAC3B,MAAM,UAAU,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,mDAAmD;IACnD,MAAM,eAAe,aAAa;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,QAAQ,UAAU,CAAC;gBACnB,QAAQ,QAAQ,CAAC;gBAEjB,MAAM,CAAC,aAAa,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACxD,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE;oBAClB,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD,EAAE;iBACzB;gBAED,QAAQ,UAAU,CAAC;gBACnB,QAAQ,qBAAqB,CAAC;YAChC,EAAE,OAAO,KAAK;gBACZ,QAAQ,QAAQ,CAAC;gBACjB,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,SAAU;gBACR,QAAQ,UAAU,CAAC;YACrB;QACF;QAEA,IAAI,UAAU;YACZ;QACF;QAEA,OAAO;YACL,QAAQ,KAAK;QACf;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI;YACF,QAAQ,UAAU,CAAC;YACnB,MAAM,iBAAiB,MAAM,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;YAClE,QAAQ,aAAa,CAAC;QACxB,EAAE,OAAO,KAAK;YACZ,QAAQ,QAAQ,CAAC;YACjB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,QAAQ,UAAU,CAAC;QACrB;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,IAAI,MAAM,OAAO,EAAE;QACjB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,8OAAC,qIAAA,CAAA,YAAS;oCAAC,aAAY;oCAAW,WAAU;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;8BAI9C,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC,oJAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;IAIxB;IAEA,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,EAAE;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCACV,MAAM,KAAK,IAAI;;;;;;sCAElB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;8CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ5E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,8OAAC,qIAAA,CAAA,YAAS;wCAAC,aAAY;wCAAW,WAAU;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;4BAGzC,CAAC,8BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kJAAA,CAAA,gBAAa;4BACZ,SAAS,MAAM,OAAO;4BACtB,cAAc;4BACd,iBAAiB;;;;;;sCAInB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAQ;;;;;;sDAC3B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAe;;;;;;sDAClC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAS;;;;;;;;;;;;8CAG9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDAEtC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC,gJAAA,CAAA,cAAW;oDAAC,OAAO,MAAM,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAGzC,8OAAC,qIAAA,CAAA,YAAS;;;;;sDAGV,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,OAAI;sEACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;;kFACrB,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAI,WAAU;kFACZ,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,4BACzC,8OAAC;gFAAyB,WAAU;;kGAClC,8OAAC;wFAAI,WAAU;;;;;;kGACf,8OAAC;wFAAK,WAAU;kGAAY,YAAY,KAAK;;;;;;kGAC7C,8OAAC;wFAAK,WAAU;kGACb,YAAY,UAAU,CAAC,kBAAkB;;;;;;;+EAJpC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;sEAYhC,8OAAC,gIAAA,CAAA,OAAI;sEACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;;kFACrB,8OAAC;wEAAG,WAAU;kFAAmB;;;;;;kFACjC,8OAAC;wEAAI,WAAU;kFACZ,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,sBACjD,8OAAC;gFAAwB,WAAU;;kGACjC,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;kGACjB,8OAAC;wFAAK,WAAU;kGAAY,MAAM,SAAS;;;;;;kGAC3C,8OAAC;wFAAK,WAAU;kGACb,MAAM,IAAI;;;;;;;+EAJL,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAerC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;8CACnC,cAAA,8OAAC,oJAAA,CAAA,kBAAe;wCACd,OAAO,MAAM,OAAO,CAAC,KAAK;wCAC1B,cAAc;;;;;;;;;;;8CAIlB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAe,WAAU;8CAC1C,cAAA,8OAAC,6IAAA,CAAA,eAAY;wCACX,cAAc,MAAM,OAAO,CAAC,YAAY;wCACxC,oBAAoB,MAAM,kBAAkB;;;;;;;;;;;8CAIhD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;8CACpC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBACzB,8OAAC,gIAAA,CAAA,OAAI;0EACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;;sGACC,8OAAC;4FAAG,WAAU;sGAAiB,MAAM,SAAS;;;;;;wFAC7C,MAAM,QAAQ,kBACb,8OAAC;4FAAE,WAAU;;gGAAgC;gGAAE,MAAM,QAAQ;gGAAC;;;;;;;;;;;;;8FAGlE,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAE,WAAU;sGAAkC,MAAM,IAAI;;;;;;sGACzD,8OAAC;4FAAE,WAAU;sGACV,MAAM,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;sFAInC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;;wFAAK;wFAAQ,MAAM,QAAQ,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;+DAlB1C,MAAM,OAAO;;;;;;;;;;;;;;;;0DA0B9B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,OAAI;0EACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;sFAAsB,MAAM,OAAO,CAAC,KAAK,CAAC,YAAY;;;;;;sFACrE,8OAAC;4EAAI,WAAU;sFAAgC;;;;;;;;;;;;;;;;;0EAGnD,8OAAC,gIAAA,CAAA,OAAI;0EACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;sFAAsB,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM;;;;;;sFAChE,8OAAC;4EAAI,WAAU;sFAAgC;;;;;;;;;;;;;;;;;0EAGnD,8OAAC,gIAAA,CAAA,OAAI;0EACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;sFACZ,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;sFAEtD,8OAAC;4EAAI,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvE;AAEe,SAAS;IACtB,qBACE,8OAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}