import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ProfileHeader } from '../profile-header';
import { mockGamerProfile } from '@/lib/mock-data';

// Mock the icons to avoid issues with Lucide React
jest.mock('lucide-react', () => ({
  MapPin: () => <div data-testid="map-pin-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
  Edit3: () => <div data-testid="edit-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Trophy: () => <div data-testid="trophy-icon" />,
  Gamepad2: () => <div data-testid="gamepad-icon" />,
  Shield: () => <div data-testid="shield-icon" />,
  Sword: () => <div data-testid="sword-icon" />,
  Heart: () => <div data-testid="heart-icon" />,
}));

describe('ProfileHeader', () => {
  const defaultProps = {
    profile: mockGamerProfile,
    isOwnProfile: false,
  };

  it('renders profile information correctly', () => {
    render(<ProfileHeader {...defaultProps} />);
    
    expect(screen.getByText(mockGamerProfile.displayName)).toBeInTheDocument();
    expect(screen.getByText(`@${mockGamerProfile.username}`)).toBeInTheDocument();
    expect(screen.getByText(mockGamerProfile.bio!)).toBeInTheDocument();
    expect(screen.getByText(mockGamerProfile.location!)).toBeInTheDocument();
  });

  it('shows online status when user is online', () => {
    render(<ProfileHeader {...defaultProps} />);
    
    expect(screen.getByText('Online')).toBeInTheDocument();
    expect(screen.getByTitle('Online')).toBeInTheDocument();
  });

  it('shows offline status when user is offline', () => {
    const offlineProfile = { ...mockGamerProfile, isOnline: false };
    render(<ProfileHeader {...defaultProps} profile={offlineProfile} />);
    
    expect(screen.getByText('Offline')).toBeInTheDocument();
  });

  it('displays gaming statistics badges', () => {
    render(<ProfileHeader {...defaultProps} />);
    
    expect(screen.getByText(`${mockGamerProfile.stats.gamesOwned} Games`)).toBeInTheDocument();
    expect(screen.getByText(`${mockGamerProfile.stats.achievementsUnlocked} Achievements`)).toBeInTheDocument();
    expect(screen.getByText(`${mockGamerProfile.stats.friendsCount} Friends`)).toBeInTheDocument();
  });

  it('shows preferred roles when available', () => {
    render(<ProfileHeader {...defaultProps} />);
    
    expect(screen.getByText('Preferred Roles')).toBeInTheDocument();
    mockGamerProfile.preferredRoles.forEach(role => {
      expect(screen.getByText(role)).toBeInTheDocument();
    });
  });

  it('shows edit button only for own profile', () => {
    const { rerender } = render(<ProfileHeader {...defaultProps} />);
    
    expect(screen.queryByText('Edit Profile')).not.toBeInTheDocument();
    
    rerender(<ProfileHeader {...defaultProps} isOwnProfile={true} />);
    expect(screen.getByText('Edit Profile')).toBeInTheDocument();
  });

  it('opens edit dialog when edit button is clicked', async () => {
    const user = userEvent.setup();
    render(<ProfileHeader {...defaultProps} isOwnProfile={true} />);
    
    const editButton = screen.getByText('Edit Profile');
    await user.click(editButton);
    
    expect(screen.getByText('Edit Profile')).toBeInTheDocument();
    expect(screen.getByLabelText('Display Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Bio')).toBeInTheDocument();
    expect(screen.getByLabelText('Location')).toBeInTheDocument();
  });

  it('calls onProfileUpdate when form is submitted', async () => {
    const user = userEvent.setup();
    const mockUpdate = jest.fn();
    render(<ProfileHeader {...defaultProps} isOwnProfile={true} onProfileUpdate={mockUpdate} />);
    
    const editButton = screen.getByText('Edit Profile');
    await user.click(editButton);
    
    const displayNameInput = screen.getByLabelText('Display Name');
    await user.clear(displayNameInput);
    await user.type(displayNameInput, 'New Display Name');
    
    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);
    
    expect(mockUpdate).toHaveBeenCalledWith({
      displayName: 'New Display Name',
      bio: mockGamerProfile.bio,
      location: mockGamerProfile.location,
    });
  });

  it('displays correct play style with appropriate styling', () => {
    render(<ProfileHeader {...defaultProps} />);
    
    const playStyleBadge = screen.getByText('Mixed Player');
    expect(playStyleBadge).toBeInTheDocument();
    expect(playStyleBadge).toHaveClass('bg-blue-100', 'text-blue-800');
  });

  it('formats join date correctly', () => {
    render(<ProfileHeader {...defaultProps} />);
    
    const joinDate = mockGamerProfile.joinDate.toLocaleDateString();
    expect(screen.getByText(`Joined ${joinDate}`)).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<ProfileHeader {...defaultProps} isOwnProfile={true} />);
    
    const editButton = screen.getByLabelText('Edit profile information');
    expect(editButton).toBeInTheDocument();
    
    const onlineIndicator = screen.getByLabelText('User is online');
    expect(onlineIndicator).toBeInTheDocument();
  });
});
